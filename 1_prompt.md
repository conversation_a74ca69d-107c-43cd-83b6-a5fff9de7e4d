## Objective: (Phase 1) Update the Database for Customizable Habit Frequency

The goal of this phase is to modify the `Habit` data model and database schema to support complex, customizable recurrence rules. This is the foundational step for the entire frequency feature.

## Implementation Plan

### Task 1.1: Extend the `Habit` Data Model

* **File to Modify:** `Habit.kt` (The Room `@Entity` file for your Habit model).
* **Action:** We need to add new columns to the `Habit` entity to store the new rules. The existing, simple frequency column should be deprecated or removed.

**Add the following new columns to the `Habit` data class:**

| Column Name | Data Type | Purpose |
| :--- | :--- | :--- |
| `frequencyType` | `String` | Stores the primary type of frequency (e.g., "DAIL<PERSON>", "WEEKLY", "MONTHLY"). |
| `repeatsEvery` | `Int` | Stores the interval (e.g., repeats every **2** weeks). Defaults to 1. |
| `daysOfWeek` | `String?` | A comma-separated string to store selected days for weekly habits (e.g., "1,4,6" for Mon, Thu, Sat). Nullable. |
| `dayOfMonth` | `Int?` | The day for monthly habits (e.g., the **15**th). Nullable. |
| `weekOfMonth` | `Int?` | The week number for advanced monthly habits (e.g., the **3rd** week). Nullable. |
| `dayOfWeekInMonth`| `Int?` | The day of the week for advanced monthly habits (e.g., **2** for Tuesday). Nullable. |

### Task 1.2: Implement the Database Migration

* **Goal:** Create a Room database migration to safely apply the schema changes from Task 1.1 without deleting existing user data.
* **Action:**
    1.  Increment the database version number in your `AppDatabase` class.
    2.  Create a new `Migration` object.
    3.  Within the `migrate()` function of your new migration, write the necessary SQL `ALTER TABLE` statements to add the new columns defined above to the `habits` table.
    4.  Add the migration to your Room database builder.

### Verification

After implementation, run the app. The app should launch without crashing, which indicates that the database migration was successful. You can further verify by using Android Studio's **App Inspection** tool to look at the database and confirm that the `habits` table now contains the new columns.

---

## ⚙️ Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.
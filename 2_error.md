[Incubating] Problems report is available at: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/build/reports/problems/problems-report.html

FAILURE: Build failed with an exception.

* What went wrong:
Cannot locate tasks that match ':app:testClasses' as task 'testClasses' not found in project ':app'.

* Try:
> Run gradle tasks to get a list of available tasks.
> For more on name expansion, please refer to https://docs.gradle.org/8.13/userguide/command_line_interface.html#sec:name_abbreviation in the Gradle documentation.
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 21s

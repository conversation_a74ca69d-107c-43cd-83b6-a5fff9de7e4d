package com.example.habits9.data

import com.example.habits9.ui.createhabit.HabitFrequency

/**
 * Enhanced frequency system for customizable habit recurrence rules.
 * This replaces the simple numerator/denominator system with more flexible options.
 */

/**
 * Enum representing the primary frequency types
 */
enum class FrequencyType(val value: String) {
    DAILY("DAILY"),
    WEEKLY("WEEKLY"),
    MONTHLY("MONTHLY");
    
    companion object {
        fun fromString(value: String): FrequencyType {
            return when (value) {
                "DAILY" -> DAILY
                "WEEKLY" -> WEEKLY
                "MONTHLY" -> MONTHLY
                else -> DAILY // Default fallback
            }
        }
    }
}

/**
 * Days of the week for weekly frequency patterns
 */
enum class DayOfWeek(val value: Int, val shortName: String, val fullName: String) {
    MONDAY(1, "Mon", "Monday"),
    TUESDAY(2, "Tue", "Tuesday"),
    WEDNESDAY(3, "Wed", "Wednesday"),
    THURSDAY(4, "Thu", "Thursday"),
    FRIDAY(5, "Fri", "Friday"),
    SATURDAY(6, "Sat", "Saturday"),
    SUNDAY(7, "Sun", "Sunday");
    
    companion object {
        fun fromValue(value: Int): DayOfWeek? {
            return values().find { it.value == value }
        }
        
        fun fromString(daysString: String?): List<DayOfWeek> {
            if (daysString.isNullOrBlank()) return emptyList()
            return daysString.split(",")
                .mapNotNull { it.toIntOrNull() }
                .mapNotNull { fromValue(it) }
        }
        
        fun toString(days: List<DayOfWeek>): String {
            return days.map { it.value }.joinToString(",")
        }
    }
}

/**
 * Enhanced frequency configuration that maps to the new database columns
 */
data class EnhancedFrequency(
    val type: FrequencyType = FrequencyType.DAILY,
    val repeatsEvery: Int = 1,
    val daysOfWeek: List<DayOfWeek> = emptyList(),
    val dayOfMonth: Int? = null,
    val weekOfMonth: Int? = null,
    val dayOfWeekInMonth: DayOfWeek? = null
) {
    
    /**
     * Convert to database representation
     */
    fun toDatabaseValues(): DatabaseFrequency {
        return DatabaseFrequency(
            frequencyType = type.value,
            repeatsEvery = repeatsEvery,
            daysOfWeek = if (daysOfWeek.isNotEmpty()) DayOfWeek.toString(daysOfWeek) else null,
            dayOfMonth = dayOfMonth,
            weekOfMonth = weekOfMonth,
            dayOfWeekInMonth = dayOfWeekInMonth?.value
        )
    }
    
    /**
     * Generate human-readable display string
     */
    fun toDisplayString(): String {
        return when (type) {
            FrequencyType.DAILY -> {
                if (repeatsEvery == 1) {
                    "Every day"
                } else {
                    "Every $repeatsEvery days"
                }
            }
            FrequencyType.WEEKLY -> {
                val daysText = when {
                    daysOfWeek.isEmpty() -> "any day"
                    daysOfWeek.size == 7 -> "every day"
                    daysOfWeek.size == 1 -> "on ${daysOfWeek.first().fullName}"
                    else -> "on ${daysOfWeek.joinToString(", ") { it.shortName }}"
                }
                
                if (repeatsEvery == 1) {
                    "Weekly $daysText"
                } else {
                    "Every $repeatsEvery weeks $daysText"
                }
            }
            FrequencyType.MONTHLY -> {
                val monthText = if (repeatsEvery == 1) "month" else "$repeatsEvery months"
                
                when {
                    dayOfMonth != null -> "Every $monthText on the ${dayOfMonth}${getOrdinalSuffix(dayOfMonth)}"
                    weekOfMonth != null && dayOfWeekInMonth != null -> {
                        val weekText = when (weekOfMonth) {
                            1 -> "1st"
                            2 -> "2nd" 
                            3 -> "3rd"
                            4 -> "4th"
                            -1 -> "last"
                            else -> "${weekOfMonth}th"
                        }
                        "Every $monthText on the $weekText ${dayOfWeekInMonth.fullName}"
                    }
                    else -> "Every $monthText"
                }
            }
        }
    }
    
    private fun getOrdinalSuffix(number: Int): String {
        return when {
            number in 11..13 -> "th"
            number % 10 == 1 -> "st"
            number % 10 == 2 -> "nd"
            number % 10 == 3 -> "rd"
            else -> "th"
        }
    }
    
    companion object {
        /**
         * Create from database values
         */
        fun fromDatabaseValues(
            frequencyType: String,
            repeatsEvery: Int,
            daysOfWeek: String?,
            dayOfMonth: Int?,
            weekOfMonth: Int?,
            dayOfWeekInMonth: Int?
        ): EnhancedFrequency {
            return EnhancedFrequency(
                type = FrequencyType.fromString(frequencyType),
                repeatsEvery = repeatsEvery,
                daysOfWeek = DayOfWeek.fromString(daysOfWeek),
                dayOfMonth = dayOfMonth,
                weekOfMonth = weekOfMonth,
                dayOfWeekInMonth = dayOfWeekInMonth?.let { DayOfWeek.fromValue(it) }
            )
        }
        
        /**
         * Common frequency presets
         */
        val DAILY = EnhancedFrequency(FrequencyType.DAILY, 1)
        val EVERY_OTHER_DAY = EnhancedFrequency(FrequencyType.DAILY, 2)
        val WEEKDAYS = EnhancedFrequency(
            FrequencyType.WEEKLY, 
            1, 
            listOf(DayOfWeek.MONDAY, DayOfWeek.TUESDAY, DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY, DayOfWeek.FRIDAY)
        )
        val WEEKENDS = EnhancedFrequency(
            FrequencyType.WEEKLY,
            1,
            listOf(DayOfWeek.SATURDAY, DayOfWeek.SUNDAY)
        )
        val WEEKLY = EnhancedFrequency(FrequencyType.WEEKLY, 1, listOf(DayOfWeek.MONDAY))
        val MONTHLY = EnhancedFrequency(FrequencyType.MONTHLY, 1, dayOfMonth = 1)
    }
}

/**
 * Data class representing the database columns for frequency
 */
data class DatabaseFrequency(
    val frequencyType: String,
    val repeatsEvery: Int,
    val daysOfWeek: String?,
    val dayOfMonth: Int?,
    val weekOfMonth: Int?,
    val dayOfWeekInMonth: Int?
)

/**
 * Backward compatibility with the old HabitFrequency system
 */
fun EnhancedFrequency.toLegacyFrequency(): HabitFrequency {
    return when (type) {
        FrequencyType.DAILY -> HabitFrequency(1, repeatsEvery)
        FrequencyType.WEEKLY -> {
            val daysCount = if (daysOfWeek.isEmpty()) 1 else daysOfWeek.size
            HabitFrequency(daysCount, 7 * repeatsEvery)
        }
        FrequencyType.MONTHLY -> HabitFrequency(1, 30 * repeatsEvery)
    }
}

/**
 * Convert legacy HabitFrequency to EnhancedFrequency (best effort)
 */
fun HabitFrequency.toEnhancedFrequency(): EnhancedFrequency {
    return when {
        denominator == 1 -> EnhancedFrequency(FrequencyType.DAILY, numerator)
        denominator > 1 && numerator == 1 -> EnhancedFrequency(FrequencyType.DAILY, denominator)
        denominator == 7 -> EnhancedFrequency(FrequencyType.WEEKLY, 1, emptyList()) // Generic weekly
        denominator == 30 -> EnhancedFrequency(FrequencyType.MONTHLY, numerator)
        denominator % 7 == 0 -> EnhancedFrequency(FrequencyType.WEEKLY, denominator / 7, emptyList())
        else -> EnhancedFrequency(FrequencyType.DAILY, 1) // Fallback
    }
}
